import { useFlowBuilderContext } from '@/contexts/FlowBuilderContext';
import { FlowNodeType, FlowTypeNode } from '@/models/flow';
import { createStyles } from '@mantine/emotion';
import { useCallback, useMemo } from 'react';
import MenuActionNode from '../MenuActionNode';
import BaseSelectNode from './BaseSelectNode';
import { Flex } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import useFlowSchema from '@/hooks/useFlowSchema';
import { CatalogNodeIcon } from '@resola-ai/ui';

interface LoopingNodeProps {
  id: string;
  data?: {
    icon?: string;
    displayName?: string;
    parentNodeId: string;
    orderedNumber?: number;
    actualParentNodeId: string;
    onEdit?: (id: string) => void;
    onDelete?: (id: string) => void;
  };
}

const useStyles = createStyles(theme => ({
  loopNodeStyle: {
    outline: `1px solid ${theme.colors.gray[4]}`,
    ':hover': {
      outline: `1px solid ${theme.colors.decaBlue[5]}`,
    },
  },
  layer1: {
    height: '6px',
    width: 'calc(100% - 10px)',
    margin: '-1px 10px 0px 10px',
    zIndex: -1,
    marginTop: '-1px',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderTopColor: 'transparent',
    borderRightColor: theme.colors.gray[3],
    borderBottomColor: theme.colors.gray[3],
    borderLeftColor: theme.colors.gray[3],
    borderImage: 'none',
    borderBottomLeftRadius: '10px',
    borderBottomRightRadius: '10px',
    boxSizing: 'border-box',
    backgroundColor: 'white',
  },
  layer2: {
    height: '5px',
    width: 'calc(100% - 20px)',
    margin: '-2px 10px 0px 10px',
    zIndex: -2,
    marginTop: '-1px',
    borderWidth: '1px',
    borderStyle: 'solid',
    borderTopColor: 'transparent',
    borderRightColor: theme.colors.gray[3],
    borderBottomColor: theme.colors.gray[3],
    borderLeftColor: theme.colors.gray[3],
    borderImage: 'none',
    borderBottomLeftRadius: '10px',
    borderBottomRightRadius: '10px',
    boxSizing: 'border-box',
    backgroundColor: 'white',
  },
}));

export default function LoopingNode({ id, data }: LoopingNodeProps) {
  const { classes } = useStyles();
  const { t } = useTranslate('flow');
  const { schema: { displayName: loopingDisplayName = '', name = 'loop' } = {} } = useFlowSchema({
    flowNodeType: FlowNodeType.Looping,
  });
  const { flowActionHandlers, handleOpenFormPanel, currentSelectNodeId } = useFlowBuilderContext();
  const onEdit = useCallback(() => {
    handleOpenFormPanel({
      nodeId: id,
      orderNumber: data?.orderedNumber ?? 0,
      type: FlowNodeType.Looping,
      parentNodeId: data?.actualParentNodeId ?? '',
    });
  }, [data, id, handleOpenFormPanel]);

  const onDelete = useCallback(() => {
    flowActionHandlers.handleRemoveNode({
      nodeId: id,
      typeRemove: FlowTypeNode.Looping,
      parentId: data?.actualParentNodeId,
    });
  }, [flowActionHandlers, id, data?.actualParentNodeId]);

  const orderedNumber = data?.orderedNumber ?? 0;
  const isSelected = useMemo(() => {
    return currentSelectNodeId === id;
  }, [currentSelectNodeId, id]);
  return (
    <Flex direction='column' justify='center' align='center' data-testid='looping-node'>
      <BaseSelectNode
        id={id}
        icon='looping'
        dataTestId='looping-node'
        openModalAction={onEdit}
        title={loopingDisplayName || t('loopingLabel')}
        baseStyle={!isSelected ? classes.loopNodeStyle : ''}
        customIcon={<CatalogNodeIcon name={name} size={20} />}
        description={
          data?.displayName
            ? `${orderedNumber}. ${data?.displayName}`
            : `${orderedNumber}. ${t('defaultNameActionNode')}`
        }
        menuContent={<MenuActionNode onDelete={onDelete} onEdit={onEdit} />}
      />
      <LayerBehinds />
    </Flex>
  );
}

const LayerBehinds = () => {
  const { classes } = useStyles();
  return (
    <>
      <div className={classes.layer1} />
      <div className={classes.layer2} />
    </>
  );
};
