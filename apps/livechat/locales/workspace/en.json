{"auto_reply.title": "Auto reply settings", "auto_reply.out_of_working.title": "Auto reply outside business hours", "auto_reply.out_of_working.description.off": "If you turn on automatic reply, a settings message will be automatically sent when a message is received from a customer.", "auto_reply.out_of_working.description.on": "If turned on, this automated message you set for customer inquiries will be automatically sent when the business status is 'Preparing'. ", "auto_reply.send_message_to_awaiting_user.title": "Auto reply to waiting customers", "auto_reply.send_message_to_awaiting_user.description": "If turned on, this automated message will be automatically sent to inquiries that have not been assigned a representative after the specified time has elapsed since the start of a attended chat.", "auto_reply.send_message_to_awaiting_user.setting_time.first": "This automated message will be sent to customer after", "auto_reply.send_message_to_awaiting_user.setting_time.first.1": "Another automated message will be sent to customer", "auto_reply.send_message_to_awaiting_user.setting_time.first.later_part": "minute(s)", "auto_reply.send_message_to_awaiting_user.setting_time.first.later_part.1": "minute(s) after the previous one", "auto_reply.send_message_to_awaiting_user.add_new_setting_time.button": "Add", "auto_reply.send_message_to_awaiting_user.option_send.option_1.first_part": "Repeat up to", "auto_reply.send_message_to_awaiting_user.option_send.option_1.later_part": "times until someone replies", "auto_reply.send_message_to_awaiting_user.option_send.option_2": "Do not repeat", "required_field": "This field is required", "save_btn": "Save", "character": "character", "automationList.title": "Automation List", "automationList.description": "Automation is a feature that responds to inquiries according to preset rules. This allows automated processes to provide an initial response to inquiries, even when real-time chat support is difficult.", "action.create": "Create a New Automation", "action.edit": "Edit Automation", "delete_confirm_message": "Are you sure you want to delete this automation?", "delete_button": "Delete", "cancel_button": "Cancel", "no_automation": "No automations were created", "trigger_not_set": "Creating an automation requires at least one trigger and one action.", "active": "Active", "inactive": "Deactive", "true_branch": "Match", "false_branch": "Unmatch", "channel_default_ocs": "Self Channel", "ocs_channel_label": "LINE {number} for main store", "condition.whenNewMessageCome": "When a Livechat is started", "condition.whenOperatorAssigned": "When a chat reply is received", "condition.whenChatIsClosed": "When a Livechat is finished", "backToAutomationList": "Back to Automation List", "detail.defaultName": "Automation", "conditionNode.label.user": "If the user is a repeater", "conditionNode.label.user.yes": "If the user is a repeater", "conditionNode.label.user.no": "If the user is not a repeater", "conditionNode.label.user.crm.label": "If the user matches with the CRM column", "conditionNode.label.conversation_original": "If the chat channel is \n{name}", "conditionNode.label.conversation.first": "If the chat channel is", "conditionNode.label.conversation.last": " ", "detail.trigger.label": "<PERSON><PERSON>", "detail.trigger.select.label": "Select Trigger", "detail.finish": "Finish", "detail.save": "Save", "detail.trigger.cancel": "Cancel", "detail.trigger.save": "Save", "detail.trigger.create": "Create", "detail.trigger.title": "Select Trigger", "detail.trigger.description": "Select how to trigger this automation", "action.add.new.condition": "Add condition", "action.add.new.action": "Add action", "action.add.new.branch": "branch", "detail.condition.title": "Select Condition", "detail.condition.select.label": "Condition", "detail.condition.select.placeholder": "Select Condition", "detail.condition.select.subject.label": "Target", "detail.condition.select.subject.option.1": "User", "detail.condition.select.subject.option.2": "Cha<PERSON>", "detail.condition.select.condition.label": "Conditional Items", "detail.condition.select.condition.option.1": "<PERSON><PERSON><PERSON>", "detail.condition.select.condition.option.2": "Cha<PERSON>", "detail.condition.1": "User is a repeater", "detail.condition.2": "User ID is equal to one of the following", "detail.condition.3": "Channel is equal to one of the following", "detail.condition.4": "Chat status is equal to one of the following", "detail.condition.5": "Chat message contains one of the following words", "detail.condition.add.more": "Add one More Condition", "detail.condition.radio.value.label": "Value", "detail.condition.radio.value.radio.label.1": "Yes", "detail.condition.radio.value.radio.label.2": "No", "detail.condition.select.condition.option.2.sub.1": "Channel", "detail.condition.select.condition.option.2.sub.2": "Assigned Team", "detail.condition.select.condition.option.2.sub.3": "Assignee", "detail.condition.select.condition.option.2.sub.4": "Person in charge availability status", "detail.condition.select.condition.option.2.sub.4.radio.1": "Present", "detail.condition.select.condition.option.2.sub.4.radio.2": "Away", "detail.condition.select.condition.option.2.sub.5": "Auto-closed conversation", "detail.condition.select.condition.option.2.sub.5.radio.1": "Yes", "detail.condition.select.condition.option.2.sub.5.radio.2": "No", "detail.condition.select.condition.option.2.sub.6": "CRM", "detail.condition.select.condition.option.2.sub.6.link_text": "Install the CRM", "detail.condition.select.condition.option.2.sub.6._column_select": "Column", "detail.condition.select.condition.option.2.sub.6._column_error": "This column is removed from CRM.", "detail.condition.select.condition.option.2.sub.6._value_placeholder": "Free Input", "detail.action.title": "Select an Action", "detail.action.select.label": "Action", "detail.action.select.placeholder": "Select Action", "detail.action.1": "Assign to specified team", "detail.action.2": "Assign to specified operator", "detail.action.3": "Send chat", "detail.condition.filter.label": "Filter", "detail.action.select.label.1": "Assign", "detail.action.select.label.2": "Send a Chat", "detail.action.button.label.1": "Select an assignee", "detail.action.message.missing_params": "Application assignment is required", "detail.action.selected_info.title": "Selected:", "detail.action.selected_info.team": "Team: {name}", "detail.action.selected_info.assignee": "Assignee: {name}", "detail.action.select_2.label": "Channel", "detail.action.text_editor.label": "Message", "detail.action.text_editor.placeholder": "Please enter your message", "detail.action.enable_period.label": "Use date and time settings", "detail.action.select_3.day_after.label": "Days Later", "detail.action.select_3.day_after.description": "(Can be set up to 30 days)", "detail.action.select_3.hour_after.label": "After hours", "detail.action.select_3.day_hour_after.label_1": "Days later", "detail.action.select_3.day_hour_after.label_2": "hour", "detail.action.select_3.day_hour_after.label_3": "minute", "detail.action.unspecified_assignee": "Unspecified", "detail.action.no_result_search_operator": "No matching results", "detail.action.search_count_message": "Search results for \"{search}\" : {number}", "detail.action.panel_assign.title": "Select an assignee", "detail.action.panel_assign.description": "Please select a team and then select an assignee.", "detail.action.panel_assign.search_placeholder": "Search", "detail.action.panel_assign.clear_search": "Clear", "condition_type.and": "All conditions are matched (AND)", "condition_type.or": "At least one condition is matched (OR)", "action_label_assign": "Assign to {name}", "action_label_no_assign": "Assign", "action_label_send_message": "Send chat for {channel} head office after {time}", "action_label_send_message_only": "Send chat", "please_select_one_option": "Please select one option", "exceed_limit_warning": "The number of items has reached the limit, \nNo new items can be added.", "close": "Close", "action.assign.select_box_title": "Assignment settings", "action.assign.select_box.option_1": "Assign to Team", "action.assign.select_box.option_2": "Assign to the assignee", "action.assign.select_box.option_3": "Reassign to the previous person in charge", "action.button.select_team.title": "Select a Team", "action.assign_method.title": "Assignment Methods", "action.assign_method.option_1": "Round Robin", "action.assign_method.option_1.tooltip": "Assign operators one by one in turn", "action.assign_method.option_2": "Random", "action.assign_method.option_2.tooltip": "Select and assign an available operator randomly", "action.assign_method.option_3": "Sorted by number of inquiries", "action.assign_method.option_3.tooltip": "Assign to operators with the fewest incoming and active cases first.", "action.assign_method.option_4.title": "Conversation Assignment Priority Settings", "action.assign_method.option_4": "Prioritize Assigning Pending Conversations", "action.assign_method.option_4.tooltip": "When a new conversation is created, prioritize assigning unassigned pending conversations first.", "action.assign_method.option_5": "Unspecified", "action.assign_method.option_5.tooltip": "Assign to a team without specifying an assignee", "action.assign_method.option_6": "Auto-assign a new conversation when one ends", "action.assign_method.option_6.tooltip": "When set to ON, a new unassigned conversation will automatically be assigned to operator after they close one (if any are available)", "action.assign_team.max_conversation.title": "Limit number of correspondence", "action.assign_team.max_conversation.description": "Number of inquiries per agent", "action.assign_team.max_conversation.limit_label": "Limit to", "action.webhook.title": "Webhook", "action.webhook.option_label": "Send To Webhook", "team_panel.title": "Select a Team", "team_panel.team_title": "Team", "team_panel.operator_title": "Assignee", "action.assign.select_box.option_3.checkbox_title": "Do not assign if previous assignee does not exist", "action.node.operator_or_team.selected": "Assigned to {name}", "action.error.missing_team_id": "Team selection is mandatory", "action.error.missing_operator_id": "Assignee selection is mandatory", "condition.node.team_selected.label_original": "If the team in charge of the chat corresponds to\n{name}", "condition.node.team_selected.label.first": "If the team in charge of the chat corresponds to", "condition.node.team_selected.label.last": " ", "condition.node.operator_selected.label_original": "If the person in charge of the chat corresponds to\n{name}", "condition.node.operator_selected.label.first": "If the person in charge of the chat corresponds to", "condition.node.operator_selected.label.last": " ", "condition.node.presence_assignee_selected.label.true": "If the chat person's presence status is\nPresent", "condition.node.presence_assignee_selected.label.false": "If the chat person's presence status is\nAway", "condition.node.auto_closed_conversation.label.true": "If the chat is auto-closed \nconversation", "condition.node.auto_closed_conversation.label.false": "If the chat is not auto-closed \nconversation", "closeRoomBtn": "Finish", "wrapUpBtn": "Post-processing", "confirmCloseConversationMessage": "Are you sure you want to finish the conversation?", "explainMessageCloseConversation": "When a conversation finishes, it will be shown in the [Finished] tab", "confirmClosedButton": "Finish", "cancelClosedButton": "Cancel", "noPermission": "You do not have permission to view this conversation, please assign yourself or assign your team into this conversation to view it", "new": "User started a attended chat", "completed": "Completed", "unsupported.placeholder": "This file format is currently not supported, so it could not be sent.", "unsupported.sticker.placeholder": "The user sent a sticker but it could not be received.", "unsupported.image.placeholder": "The user sent an image but it could not be received.", "activity.completed": "Chat was completed by {operatorName}", "activity.assigned.you": "You were assigned", "activity.assigned.operator": "{operator<PERSON><PERSON>} was assigned", "activity.assigned.operator.unknown": "{operatorName} has been removed from assignee assignment", "activity.assigned.team": "{team<PERSON><PERSON>} was assigned", "activity.inwrapup": "Post-processing started by {name}", "activity.inwrapup.system": "Post-processing has started due to Auto-close conversation settings", "ai.summary.tooltip": "AI Summary", "ai.summary.content.title": "[AI Summary]", "bot": "Bot", "create_new_conversation": "Start a new attended chat", "confirm_move_to_new_conversation": "This user is currently connected to an attended chat.\nAre you sure you want to move to the corresponding inquiry?", "goto_other_conversation": "Proceed", "confirmWrapUpConversation": "Do you want to start post-processing?", "explainWrapUpConversation": "Please note that once you begin wrap-up, you will not be able to resume the conversation with the user.", "wrapUpButton": "Start post-processing", "conversationCompletedByUser": "Chat was completed by user", "conversationCompletedBySystem": "Conversation ended by auto-close setting", "autoCloseTooltipText": "Auto-Close is ", "autoCloseMenuChangeTime": "Change the Auto-Close Time", "autoCloseMenuToggleOn": "Turn On the Auto-Close", "autoCloseMenuToggleOff": "Turn Off the Auto-Close", "autoCloseMenuTitle": "Change the Auto-Close Time", "autoCloseMenuTextOne": "The conversation will be auto closed after", "autoCloseMenuTextTwo": "since last message", "minutes": "mins", "hours": "h", "filter.new": "Incoming", "filter.processing": "Ongoing", "filter.done": "Finished", "platform.line": "LINE", "platform.web": "Web", "no.conversation": "There are no inquiries yet.", "wrapUpBadge": "Post-processing", "noAssignee": "No Assignee", "hasAssigneeName": "Assignee : {name}", "errorMessageSelfAssignAlreadyTaken": "The conversation has already \nbeen assigned to another operator.", "errorMessageSelfAssignAlreadyTakenCaseAnother": "This conversation has already been assigned to someone else", "cancel": "Cancel", "placeholder": "Please write the message here", "sendMessage": "Send", "aiAutoCorrect": "AI Auto Correct", "dropZone.description": "You can add images by drag and drop", "dropZone.warning1": "You can upload multiple files, but there are limits to the types and sizes ", "dropZone.warning2": "of files you can upload.", "dropZone.warning3": "Image: png, jpg, jpeg, svg (1 file 5MB or less)", "asset.upload.error.filetoolarge": "File is larger than {LIMIT_SIZE} MB", "asset.upload.error.fileinvalidtype": "The file format seems not to be supported. Please select a file in .png .jpg .jpeg .svg format", "asset.upload.error.filenotsupport": "The file format seems not to be supported. Please select a file in .png .jpg .jpeg .svg format", "asset.upload.error.toomanyfiles": "Too many files, we do not support uploading more than {LIMIT_FILE_PER_UPLOAD} files at a time", "aiAutoAdjustmentTitle": "AI Auto Re-write", "autoAdjustment": "Nuance", "freeToSpecifyTitle": "Prompt Free Input", "freeToEnterPlaceHolder": "Please feel free to input", "letter": " Characters", "save": "Save", "saved": "Saved", "editorAutoReplyPlaceholder": "Thank you for your inquiry. We apologize for the inconvenience, but we are currently making preparations, so it may take some time for us to respond to you.", "tag.correction.label": "Revise", "tag.correction.value": "CORRECT_SENTENCE", "tag.polite.label": "Polite", "tag.polite.value": "TONE_POLITE", "tag.friendly.label": "Friendly", "tag.friendly.value": "TONE_FRIENDLY", "tag.softer.label": "Soft", "tag.softer.value": "TONE_SOFTER", "tag.brighter.label": "<PERSON>", "tag.brighter.value": "TONE_BRIGHTER", "tag.longer.label": "Longer", "tag.longer.value": "MAKE_SENTENCE_LONGER", "tag.shorter.label": "Shorter", "tag.shorter.value": "MAKE_SENTENCE_SHORTER", "reply.organization": "Reply As Official Account", "reply.team": "Reply As Team", "reply.user": "Reply As Assignee", "externalService.pageTitle": "External Tools Integration", "tab.integrated.tools": "Integrated Tools", "tab.all.tools": "All Tools", "tool.integrated": "Integrated", "no.integrated.tool": "There are no integrated external tools.", "all.tools.list": "All Tools List", "take.a.look.available.tooks": "Take a look and take advantage of the various tools available.", "button.integrated": "Integrate", "button.cancel": "Cancel", "cooperation": " Integration", "lineSettingDescription": "Copy the channel ID and channel secret from LINE Developers and paste them here.", "lineIntegrationDescription": "You can send and receive messages by integrating with your LINE official account.", "webhookIntegrationDescription": "Webhooks can be used to send events such as conversation history to external sources in real time.", "channelNameLabel": "Channel Name", "channelNamePlaceholder": "Please enter your name", "yourUserIdLabel": "Your User Id", "webhookUrlLabel": "Webhook URL", "channelIDLabel": "Channel Id", "channelSecretLabel": "Channel Secret", "accessTokenLabel": "Access Token", "basicIDLabel": "Bot Basic Id", "copy": "copy", "cancelToolPackage": "Cancel linkage", "cancelConfirmMessage": "Are you sure you want to cancel the link?", "button.unlock": "Unlock", "error.msg.field.required": "Please enter the required field", "error.msg.field.channelName.duplicated": "This channel name is already used", "count.characters": "{totalChars} characters left", "error.msg.field.channelId.duplicated": "This channel ID is already used", "webook_error_modal_description_first": "This webhook appears to be being used by an automation.\nYou can't unlink it.", "webook_error_modal_description_link": "Automation Page", "webook_error_modal_description_last": "Please check ", "webook_error_modal_btn": "Close", "webhook.back_link_title": "Back to the External Service", "webhook.page_title": "Webhook Integration", "webhook.page_description": "Please enter details of the external service you are sending the request to.", "webhook.field.name.label": "Name", "webhook.field.name.placeholder": "This is a name for management. It will not be disclosed to users.", "webhook.field.method.label": "Method", "webhook.field.url.label": "URL", "webhook.field.header.label": "Headers", "webhook.field.header.placeholder.key": "Key", "webhook.field.header.placeholder.value": "Value", "webhook.field.payload.label": "Payload", "webhook.field.payload.option.transform": "Event Payload", "webhook.field.payload.option.custom": "Custom Payload", "webhook.field.payload.textarea.custom.placeholder": "Please enter payload", "webhook.field.transform.label": "Transformer", "webhook.field.transform.button.label": "Apply Transformer", "webhook.field.transform.modal.title": "Sample payload", "webhook.field.transform.modal.edit": "Edit", "webhook.field.transform.modal.environment_variable": "Variable", "webhook.field.transform.modal.edit.placeholder": "Edit here...", "webhook.field.transform.custom_payload.edit.placeholder": "Please enter your custom payload here.", "webhook.field.transform.modal.preview": "Preview", "webhook.field.auth0.checkbox.label": "OAuth2", "webhook.field.auth0.checkbox.description": "Enable", "webhook.field.auth0.grant_type.label": "Grant Type", "webhook.field.auth0.grant_type.option_password": "Password", "webhook.field.auth0.client_id.label": "Client ID", "webhook.field.auth0.client_id.placeholder": "Client ID", "webhook.field.auth0.client_secret.label": "Client Secret", "webhook.field.auth0.client_secret.placeholder": "Client Secret", "webhook.field.auth0.user_id.label": "User ID", "webhook.field.auth0.user_id.placeholder": "User ID", "webhook.field.auth0.password.label": "Password", "webhook.field.auth0.password.placeholder": "Password", "webhook.field.auth0.scope.label": "<PERSON><PERSON>", "webhook.field.auth0.scope.placeholder": "eg: read:org", "webhook.field.auth0.access_token_url.label": "Access Token URL", "webhook.field.auth0.access_token_url.placeholder": "https://", "webhook.field.auth0.access_token_path.label": "Access Token Path", "webhook.field.auth0.access_token_path.placeholder": " ", "webhook.button.test.label": "Test Webhook", "webhook.button.save.label": "Integrate", "webhook.remove_integration.label": "Remove Integration", "conversation.created": "When starting a manned chat", "conversation.message.received": "When operator is assigned", "conversation.closed": "When chat is closed", "requiredField": "This field is required", "wrongFormat": "Please enter a valid data", "char": " Characters", "warning_exit": "Changes you make may not be saved.", "success_label": "Success", "error_label": "Error", "error_description": "An error occurred while accessing URL", "integration_type": "Integration Type", "chatwindow": "Chat Window", "modal.transformer.variable.placeholder": "Custom Item", "modal.transformer.variable.variable_key": "Variable", "modal.transformer.variable.variable_value": "Value", "modal.transformer.variable.variable_value_runtime_content": "Calculate in run time", "modal.transformer.variable.button_add_row": "Add", "modal.transformer.variable.button_add_row.error_tooltip": "The number of variable has reached the limit,\nno more can be added", "modal.transformer.variable.error_same_variable_name": "This name is already in use", "modal.transformer.variable.error_enter_required_field": "Please enter the required fields", "profile": "Profile", "logout": "Logout", "accountSettings": "Profile", "decaChatbot": "DECA Chatbot", "decaChatwindow": "DECA Chatwindow", "decaKnowledgeBase": "DECA Knowledge Base", "decaLiveChat": "DECA Livechat", "decaCRM": "DECA CRM", "decaForms": "DECA Forms", "decaTables": "DECA Tables", "decaAIWidgets": "DECA AI Widgets", "decaPages": "DECA Pages", "decaAiStudio": "DECA AI Studio", "inbox.title": "Me", "inbox.myassigned.title": "My Assigned", "inbox.myassigned.assigned.title": "All", "inbox.myassigned.bookmark.title": "Bookmark", "team_detail.user.status.title": "Status", "inbox.myassigned.online.title": "Active", "inbox.myassigned.offline.title": "Inactive", "inbox.team.title": "Team", "inbox.team.edit.title": "Edit Team", "inbox.team.manage.title": "Manage Member", "inbox.team.unassigned.title": "No assignee", "inbox.team.all.title": "All", "settings.title_menu": "Settings", "settings.team_user_menu_label": "Team & User Management", "settings.business_status_menu_label": "Business Status Management", "settings.auto_reply_settings_label": "Auto Reply Settings", "settings.external_tool_integration_label": "External Status Integration", "settings.notification_settings": "Notification Settings", "settings.function_settings": "Functionality Settings", "settings.personal_settings": "Notification Setting", "settings.auto_assignment": "Automatic Assignment", "settings.auto_assignment_modal": "There are currently no unassigned conversations.", "personal.setting.title": "Notification Setting", "conversation.setting.title": "Conversation Settings", "notification.with_sound": "Notify with sound", "notification.sound": "Notification sound", "notification.sound.label": "Sound {index}", "notification.sound.option.none": "Don't notify", "notification.sound.option.first": "First message only", "notification.sound.option.all": "All messages", "notification.sound.option.sound": "Sound ", "method.send_message.title": "How to send a message", "method.send_message.enter": "Enter", "method.send_message.enter_shift": "Shift + Enter", "title": "Notification settings", "description": "You can customize how you receive notifications about your inquiries.", "notification.ticket_assign_to_me_label": "Inquiry assigned to me", "notification.ticket_assign_to_team_label": "Inquiries assigned to my team", "notification.ticket_assign_to_unknown_label": "An inquiry with no assigned assignee", "notification.ticket_pic_change_or_assign_to_me_label": "When the person in charge changes and is assigned to you", "notification.type.in_app": "In-app notification", "notification.type.email": "Email notification", "notification.notify.when_new_message": "All messages", "notification.notify.when_start_conversation": "Only at start", "operation_title": "Business Status Management", "operation_status_title": "Current business status", "operation_status_description": "Turn on to accept chat.", "operation_auto_change_status_title": "Automatic change of business status", "operation_auth_change_status_description": "When turned on, the \"Current Business Status\" will be automatically changed according to the \"Business Hours Settings\" and \"Holiday Settings\" below. ", "operation_business_hour_title": "Set business hours", "operation_business_hour_collective_setting": "Collective setting", "apply_to_all_button": "Apply to all", "hour": "hour", "minute": "minute", "day.monday": "Mon", "day.tuesday": "<PERSON><PERSON>", "day.wednesday": "Wed", "day.thursday": "<PERSON><PERSON><PERSON>", "day.friday": "<PERSON><PERSON>", "day.saturday": "Sat", "day.sunday": "Sun", "operation_holiday_setting_title": "Holidays Setting", "operation_holiday_setting_description": "You can add holidays by pressing the button below. The business status of registered holidays will be \"Preparing (holiday setting)\"", "add_holiday_button": "Add Holidays", "add_holiday_title": "Add a Holiday", "input_holiday_name_label": "Holiday Name", "input_holiday_default_value": "Holiday", "input_date_time_label": "Select Date & Time", "date_time_selected": "Date selected:", "add_label": "Add", "date_label": "Date", "edit_label": "Edit", "delete_label": "Delete", "edit_holiday_label": "Edit Holiday", "save_label": "Save", "delete_holiday_confirm_message": "Are you sure you want to delete this holiday?", "delete_btn_label": "Delete", "from": "from", "to": "to", "operation_auto_reply_setting_title": "Auto reply outside business hours", "operation_auto_reply_setting_description_on": "When turned on, the message set for customer inquiries will be automatically sent when the business status is \"Preparing\"", "operation_auto_reply_setting_description_off": "If you turn on auto-reply, a settings message will be automatically sent when a message is received from a customer.", "start_stop_services_title": "Starting/Stopping services", "status_open": "Open", "status_preparing": "Preparing", "operation_business_status_description": "If you turn it off, the business status will become \"Preparing\" and you will stop accepting chats. Please use it in case of emergency.", "apply": "Apply", "error_choose_date_range": "Please choose a date", "holiday_setting": "(Holiday Settings)", "invalid_time_input": "Please select an end time that is later than the start time.", "notification.event.message.new": "use} has sent you a message", "user.anonymous": "Anonymous", "notification.event.conversation.created": "use} has started a conversation.", "notification.event.conversation.status.updated": "use} has updated the status of the conversation to statu}.", "notification.event.conversation.closed": "use} has closed the conversation.", "conversation.status.new": "New", "conversation.status.inProgress": "In Progress", "conversation.status.completed": "Completed", "notification.event.conversation.operator.assigned.title": "User: nam}", "notification.event.conversation.operator.assigned.description": "Has been assigned to operato}", "notification.event.conversation.operator.no_data": "No operator is assigned the inquiry", "chatbot": "Chatbots", "chatbot_title_modal": "Chatbot customer information", "customer_name": "Customer Name", "email": "Email", "phone_number": "Phone number", "company": "Company", "memo": "Memo", "custom_items": "Custom Items", "labels": "Labels.", "more": "More", "chatbot_history": "Chatbot History", "readmore": "Read more", "no_message_history": "No message history", "search_placeholder": "Search from loaded content", "widget_from_chatbot": "To display this widget, \nyou need to work with LIBERO", "change_warning": "Changes made may not be saved.", "search_label": "Search", "search_result_count": "Search Result: {count}", "no_search_result": "No matching search results", "warningResult": "The search will cover messages from the past three months", "preBtn": "Back", "nextBtn": "Next", "main.title": "There is no assignee for this inquiry yet", "main.description": "If you would like to become the assignee, please press the below button.", "main.submit": "Assign to Me", "popup.selfAssign.title": "Do you want to assign yourself to this inquiry?", "popup.selfAssignTeam.description1": "It seems that you belong to multiple teams.", "popup.selfAssignTeam.description2": "Please select the team you want to assign", "popup.selfAssignTeam.label": "Assigned Team", "popup.selfAssignTeam.placeholder": "Please select the assigned team", "popup.selfAssign.cancel": "Cancel", "popup.selfAssign.submit": "Assign", "popup.selfAssign.description": "If you want to assign another person, please change it in \"Reception Status\"", "popup.selfAssignTeam.noTeam": "You do not belong to any team", "pageTitle": "Team & User Management", "tab.team_list_label": "Team ", "tab.user_list_label": "User", "tab.team_list.description": "This can be used to create teams and automatically assign the team member who is in charge of customer service chat.", "tab.team_list.button_add_team_label": "Create team", "tab.team_list.input_placeholder": "Search by Team name", "tab.team_list.member_count_label": "({count} User/s)", "tab.team_list.button_disabled": "Invalid", "tab.team_list.create_team_modal.title": "Create a New Team", "tab.team_list.create_team_modal.name": "Team name", "tab.team_list.create_team_modal.name.placeholder": "Please enter your team name. It will be shown to the guest.", "tab.team_list.create_team_modal.description": "Description", "tab.team_list.create_team_modal.description.placeholder": "Please provide a description of this team", "tab.team_list.success_create_team_modal.name": "The \"UI/UX Design Department\" is created!", "tab.team_list.success_create_team_modal.description": "You are currently the only member of this team", "tab.user_list.description": "You can invite users, manage the team and user role here", "tab.user_list.button_add_user_label": "Invite user", "tab.user_list.input_placeholder": "Search by username", "tab.user_list.table.user_name": "Name", "tab.user_list.table.user_mail": "Email", "tab.user_list.table.user_role": "Role", "tab.user_list.table.team_name": "Team", "tab.user_list.table.edit_label": "Edit", "tab.user_list.pagination.forward": "Previous", "tab.user_list.pagination.next": "Next", "tab.user_list.user_role.operator": "Operator", "tab.user_list.user_role.admin": "Manager", "field_required_message": "This field is required", "only_input_number_chars": "This input is limited to {number} characters", "close_label": "Close", "confirm_label": "Confirm", "add_member_button_label": "Add a Member", "modal.add_team_member_title": "Add team member", "modal.add_team_member_description": "Please select a member", "add_member_button": "Add", "back_to_team_list_label": "Back Team & User Management", "tooltip.edit_label": "Edit", "tooltip.delete_label": "Delete From Team", "check_box_disable_team_label": "Enabled", "modal_confirm_disable_team_title": "Disable the team.\nAll operations will be disabled until\nyou enable it.", "modal_confirm_disable_team_button_confirm": "Disable", "modal_edit_member_title": "Edit User", "modal_edit_member_team_count": "Team(s) you belong to ({number})", "modal_save_button_label": "Save", "title_remove_user_from_team": "Are you sure you want to remove this user from your team? \nIf the user has been deleted, please contact the team. \nYou will no longer be able to refer to it.", "content_remove_user_from_team": "If you have a case assigned to you \nGo to the「No Person in Charge」menu", "remove_button": "<PERSON><PERSON><PERSON> from team", "no_data_label": "No Data", "duplicate_team_name": "This {team} is already in use", "no_team": "No team has been created. Please create a new team.", "search_no_result": "No matches found", "no_user": "There are no invited users", "image_label": "Avatar", "image_des": "The Avatar will be shown on guest side.", "update_image_button": "Change Avatar", "image_upload_des": "Supported file format: png (up to 1MB. 250px x 250px recommended)", "image_exceed_size": "File size limit is {number}MB", "functionSettings": "Functionality Settings", "autoConvAssign": "Automatic conversation assignment", "showAutoBtnIfOff": "Show auto-assign button in inbox", "showAutoBtnIfOffDesc": "When set to OFF, the button will be hidden.", "convEndMethod": "Conversation End Method", "canEndWithoutReply": "Can end without reply", "canEndWithoutReplyDesc": "When set to OFF, operator can only end a conversation after replying.", "default": "<PERSON><PERSON><PERSON>", "changeAutoClose": "Auto-close Conversation", "changeAutoCloseTextOne": "When there are no new messages in the conversation for", "changeAutoCloseTextTwo": ", the conversation closes automatically", "autoSendMessageBeforeComplete_1": "Send an automated message to the user after", "autoSendMessageBeforeComplete_2": "to notify them that the conversation will close", "autoSendMessageBeforeComplete_explain": "The automated message sending time must be less than the auto-close time.", "autoSendMessageBeforeComplete_defaultMessage": "Thank you for contacting us. This conversation will automatically finish in 3 minutes. We look forward to hearing from you again.", "createNewConversationLabel": "Create a new conversation", "createNewConversationOption": "Show \"Start a new attended chat\" button for conversations with \"Finished\" status", "createNewConversationText": "When set to OFF, the button will be hidden.", "aiSummaryFunction": "Setting for AI ​​summary function", "editPrompt": "Edit the Prompt", "button.save": "Save", "button.saved": "Saved", "editSummaryPrompts": "Edit Prompt for AI Summary", "defaultAIPrompt": "あなたはカスタマーサポート担当者です。以下の会話はカスタマーサポート担当者とお客様との会話のやり取りです。以下の会話のやり取りの内容を日本語で要約してください。", "aiSummaryPromptPlaceholder": "Please enter the prompt (Required)", "characterCount": " Characters", "reset": "Reset", "maxConversationSettingsTitle": "Maximum Concurrent Users", "maxConversationSettingsDescription": "You can set the number of users who can connect to Livechat simultaneously. Please input numbers from 1 to 100.", "teamListDefaultTeamTooltip": "You cannot leave this team or remove members because it is the default team.", "label.customer": "Reception Status", "label.customer.name": "Assignee", "label.customer.team": "Assigned Team", "label.customer.lastTimeChat": "Last Message", "label.customer.lastTimeChatCompleted": "Last Chat", "label.customer.equivelantTime": "Equivalent Time", "label.customer.convoCount": "Conversation count", "label.customer.convoCountTag": "th Visit", "label.customer.convoCountTagOne": "1st Visit", "label.customer.convoCountTagTwo": "2nd Visit", "label.customer.convoCountTagThree": "3rd Visit", "label.customer.convoCountTagTen": "10+ Visits", "label.customer.note": "Note", "label.customer.note.placeholder": "Enter a note (autosave)", "label.ai": "AI Reply Assist", "label.ai.btn.copy": "Copy to Message Edit", "label.ai.btn.manualTrigger": "Execute", "label.ai.defaultText": "When a message is received from a customer, the optimal reply would be automatically generated here.", "label.add_plugin.button_add_plugin": "Add widget", "modal_operator.title": "Change assignee", "modal_operator.description": "You can change the assignee here. The assignee will be notified once assigned. ", "modal_operator.input_placeholder": "Search", "modal_operator.button_cancel": "Cancel", "modal_operator.button_confirm": "Change", "modal_operator.label.no_data_matched": "No matching results", "modal_operator.button.clear": "Clear", "modal_operator.count_matched": "Search results for \"__search__\" : __number__", "chat.detail.latestChat": "Passed", "chat.detail.elapsed": "Passed", "chat.detail.operator.unassigned.title": "No Assignee", "chat.detail.team.unassigned.title": "None", "chat.detail.no.assignee": "Unspecified", "activity_widget_title": "Activity Widget", "chatbot_activity_title": "Start chatbot", "livechat_activity_title": "Start Livechat", "crm_widget_title": "CRM", "see_more": "See more", "no_activity_yet": "No activity", "search_placeholder_activity": "Search from loaded content", "end_message": "That's all for the message", "templates.title": "Reply template", "conversation.operator.assigned.you": "You are now in charge", "conversation.operator.assigned": "{name} is now the contact", "conversation.operator.assigned.unknown": "{name} has been unassigned from the contact", "conversation.team.assigned": "{name} team is now the contact", "conversation.completed": "Chat completed by {name}", "conversation_completed": "Chat completed by {name}", "conversation.completed.system": "Conversation ended by auto-close setting", "conversation.new": "User started a attended chat", "conversation.isCreated": "User started a attended chat", "memo.title": "Comments", "memo.length": " characters", "memo.placeholder": "Add a comment", "edit": "Edit", "delete": "Delete", "delete_message": "Are you sure you want to delete this comment?", "comment_count": "{count} Comments", "no_comment": "No Comment", "back_to_original_page": "Go Back to the Conversation History", "widget.title": "Widgets", "widget.tab.installed": "Installed Widgets", "widget.tab.all": "All Widgets", "widget.aiautoreplytitle": "AI Automatic Reply Assistance", "widget.chatbottitle": "<PERSON><PERSON><PERSON> Widget", "widget.activitytitle": "Activity Widget", "widget.crm.title": "CRM", "widget.form.name": "Name", "widget.form.name.placeholder": "Please enter the name", "widget.form.name.chatbot.placeholder": "Name for management purposes", "widget.form.name.activity.placeholder": "placeholder-activity", "widget.form.description": "Description", "widget.form.description.placeholder": "Please enter the description", "widget.form.prompt": "Prompt", "widget.form.prompt.placeholder": "Please enter the prompt", "widget.form.configuration": "Automatic Configuration", "widget.form.option.auto.executed": "Automatically executed after each conversations", "widget.form.option.manual.executed": "Press button to run", "widget.form.option.display.executed": "Display only", "widget.form.option.update.executed": "Display and update information on widgets", "widget.cancel": "Cancel", "widget.save": "Save", "widget.form.install.title": "Installing Widget", "widget.install": "Install", "widget.no.available": "No available widgets", "widget.uninstall": "Uninstall", "widget.uninstall.title": "{name}", "widget.uninstall.confirm": "Do you want to uninstall?", "widget.select.widget.to.use": "Please select the widget you would like to use.", "edit_user.change_status.confirm_text": "Are you sure you want to change this\n member's presence status?", "edit_user.change_status.confirm.button": "Change", "version": "Version", "ai_widget_description": "AI Auto Reply Assist is a tool that supports efficient and effective message replies.", "rebot_widget_description": "The Chatbot Widget can display LIBERO user information when integrated with LIBERO.", "activity_widget_description": "The Activity Widget allows you to view user activity history. In the current version, you can view past inquiries on human chat and conversation history with chatbots.", "crm_widget_description": "The CRM widget is a tool that displays information from DECA CRM when integrated with it.", "crm_widget.display_content.title": "Display Content", "crm_widget.display_content.crm_object.title": "Please select the CRM object you want to display in Livechat", "crm_widget.display_content.crm_fields.title": "Please select the CRM columns you want to display in Livechat", "crm_widget.display_content.crm_fields.tooltip": "These columns can be applied in Livechat automation.\nRemoving them from CRM or here will invalidate dependent automation.", "crm_widget.display_content.crm_identified_field.title": "Please select the CRM column to identify the CRM user.", "crm_widget.display_content.crm_identified_field.placeholder": "Please select the object above...", "crm_widget.display_content.crm_view.title": "Please select the CRM view you want to display in Livechat.", "crm_widget.display_content.crm_view.placeholder": "Please select the object and column above...", "crm_widget.display_content.crm_view.description": "If the selected view has been deleted from CRM, the default view will be displayed.", "crm_widget.search_no_match": "These are no results that matched your search", "crm_widget.removed_column_error": "Data is removed from CRM", "crm_widget.warning_removed_column_error": "This column is removed from CRM. ", "crm_widget.default_message.empty_data": "No customer information found in CRM", "crm_widget.default_group_name": "Other", "crm_automation.action_node.warning": "Team selection may be overridden by automatic assignment settings.", "crm_widget.sync_button": "Sync", "crm_widget.input.placeholder": "Please select...", "crm.translate.default.title": "CRM Customer info", "crm.translate.personalInfo": "Personal Info", "crm.translate.personalInfo.name": "Name", "crm.translate.personalInfo.furiganaName": "Name (<PERSON><PERSON><PERSON>)", "crm.translate.personalInfo.phone": "Phone Number", "crm.translate.personalInfo.email": "Email Address", "crm.translate.lineInfo": "LINE Info", "crm.translate.lineInfo.name": "LINE User Name", "crm.translate.lineInfo.friendRegistrationDate": "LINE Friend Registration Date", "crm.translate.lineInfo.friendAddingMethod": "LINE Friend Adding Method", "crm.translate.lineInfo.friendBlocked": "LINE Friend Block", "crm.translate.onlineReceptionInfo": "Online Reception Info", "crm.translate.onlineReceptionInfo.consultationDate": "Online Consultation Date", "crm.translate.onlineReceptionInfo.segmentCustomerAnswer": "Segment (Customer Answer)", "crm.translate.onlineReceptionInfo.segmentRounderInput": "Segment (Rounder Input)", "crm.translate.onlineReceptionInfo.genderCustomerAnswer": "Gender (Customer Answer)", "crm.translate.blank_text": "Blank", "crm.translate.yes_text": "Yes", "crm.translate.no_text": "No", "new_message_received": "New messages"}