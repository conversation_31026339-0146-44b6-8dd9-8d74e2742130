import { BaseSyntheticEvent, useCallback, useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import {
    rem,
    Container,
    Title,
    Text,
    Group,
    Button,
    SimpleGrid,
    Card,
    Switch,
    Center,
    Loader,
    ActionIcon,
    Modal,
    Flex,
    Space,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import styled from 'styled-components';
import { IconPlus, IconHandClick, IconTrash } from '@tabler/icons-react';
import { BottomUpTween } from '@resola-ai/ui';
import { useAutomationListContext } from './contexts/AutomationListContext';
import { AutomationData, Status } from '../../../models/automation';
import { useFuncFormatDate } from '../../Common';
import { COMMON_PAGE_MAX_WIDTH } from '../../../constants';
import ViewOnly from '../../../components/ViewOnly';

const useStyle = createStyles((theme) => ({
    container: {
        maxWidth: COMMON_PAGE_MAX_WIDTH,
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        overflowY: 'auto',
        minHeight: `calc(100dvh - var(--app-shell-header-height, 0px) + 0rem)`,
        '& button:disabled:not(.mantine-ActionIcon-root)': {
            borderColor: theme.colors.violet[3],
            backgroundColor: theme.colors.violet[3],
            color: '#fff',
            cursor: 'not-allowed',
        },
        '& button.mantine-ActionIcon-root:disabled': {
            color: theme.colors.violet[3],
            cursor: 'not-allowed',
            backgroundImage: 'none',
            pointerEvents: 'none',
        },
        '& .mantine-Switch-root input:disabled:checked + label.mantine-Switch-track': {
            backgroundColor: theme.colors.violet[3],
            borderColor: theme.colors.violet[3],
        },
    },
    colorViolet: {
        color: theme.colors.navy[0],
    },
    cardItem: {
        cursor: 'pointer',
        '&:hover': {
            borderColor: theme.colors.navy[0],
        },
    },
    cardTitle: {
        cursor: 'pointer',
        fontSize: 16,
        fontWeight: 700,
        color: '#373A40',
    },
    cardDate: {
        fontSize: 14,
        fontWeight: 400,
        color: '#868E96',
    },
    closeIcon: {
        '&:hover': {
            backgroundColor: 'transparent',
        },
    },
    switchStyle: {
        '& .mantine-Switch-labelWrapper': {
            marginRight: '5px',
            minWidth: '50px',
        },
    },
}));

const SwitchStyled = styled(Switch)`
    cursor: pointer;
`;

export function AutomationListScreen() {
    const { classes } = useStyle();
    const {
        t,
        sortedData: automationList = [],
        toggleActiveState,
        isLoading,
    } = useAutomationListContext();
    const triggerLabels = useMemo(() => {
        return {
            'conversation.closed': t('condition.whenChatIsClosed'),
            'conversation.created': t('condition.whenNewMessageCome'),
            'conversation.message.received': t('condition.whenOperatorAssigned'),
        };
    }, [t]);

    const getTriggerLabel = useCallback(
        (code: string) => {
            return triggerLabels?.[code] || '';
        },
        [triggerLabels],
    );

    const handleToggleState = useCallback(
        async (automation: AutomationData, activeState: Status) => {
            const newAuto = JSON.parse(JSON.stringify(automation)) as AutomationData;
            const id = newAuto.id;
            newAuto.flow = JSON.parse(newAuto.flow as string);
            newAuto.status = activeState;
            await toggleActiveState(id, newAuto);
        },
        [toggleActiveState],
    );

    return (
        <Container fluid px={0} className={classes.container}>
            <Group justify="flex-start" align="flex-end">
                <Title order={3} mt={'lg'} fw={700}>
                    {t('automationList.title')}
                </Title>
                <ViewOnlyBadgeForNotManager />
            </Group>

            <Text mt={'lg'} size={'14px'} c={'gray.7'} sx={{ lineHeight: '1.35rem' }}>
                {t('automationList.description')}
            </Text>
            {isLoading ? (
                <>
                    <Center mt={'lg'}>
                        <Loader type="dots" color="navy.0" />
                    </Center>
                </>
            ) : (
                <>
                    {automationList?.length ? (
                        <Group justify="flex-end" mt={'md'}>
                            <CreateAutomationButton />
                        </Group>
                    ) : null}
                    <SimpleGrid my={'lg'} spacing={'lg'} cols={{ xs: 1, sm: 2, md: 2 }}>
                        {automationList?.length
                            ? automationList.map((automation, index) => {
                                  return (
                                      <BottomUpTween key={automation.id} delay={0.1 + index / 10}>
                                          <AutomationItem
                                              automation={{
                                                  ...automation,
                                                  desc: getTriggerLabel(automation.trigger),
                                              }}
                                              onToggleActive={(active: Status) =>
                                                  handleToggleState(automation, active)
                                              }
                                          />
                                      </BottomUpTween>
                                  );
                              })
                            : null}
                    </SimpleGrid>
                    {!automationList?.length ? (
                        <Flex
                            direction={'column'}
                            align={'center'}
                            justify={'center'}
                            style={{ flexGrow: 1 }}
                        >
                            <Text color="gray.6" size={rem(14)} fw={400} mb="lg">
                                {t('no_automation')}
                            </Text>
                            <CreateAutomationButton />
                        </Flex>
                    ) : null}
                </>
            )}
            <ConfirmDeleteAutomationModal />
        </Container>
    );
}

function ViewOnlyBadgeForNotManager() {
    const { isManager, isLoadingProfile } = useAutomationListContext();
    if (isLoadingProfile || isManager) return null;
    return <ViewOnly />;
}

function CreateAutomationButton() {
    const { push } = useRouter();
    const { t, isManager } = useAutomationListContext();

    return (
        <Button
            size="sm"
            variant="filled"
            color="navy.0"
            disabled={!isManager}
            radius={'sm'}
            pl="sm"
            pr="sm"
            onClick={() => isManager && push('/automation/create')}
        >
            <IconPlus size={'20px'} strokeWidth={3} />
            <Space w={5} />
            <Text fz={14} ml={2} fw={700}>
                {t('action.create')}
            </Text>
        </Button>
    );
}

type AutomationItemProps = {
    automation: AutomationData;
    // eslint-disable-next-line no-unused-vars
    onToggleActive: (active: Status) => void;
};
function AutomationItem({ automation, onToggleActive }: AutomationItemProps) {
    const { classes } = useStyle();
    const { push } = useRouter();
    const { t, intendToDeleteAutomation, isManager = false } = useAutomationListContext();

    const formatDate = useFuncFormatDate();
    const { id = '', name = '', status, desc: descriptionAction = '', created } = automation;
    const [currStatus, setCurrStatus] = useState('active');

    useEffect(() => {
        status && setCurrStatus(status);
    }, [status]);

    const handleMoveToDetail = useCallback(
        (e: BaseSyntheticEvent) => {
            const target = e.target as HTMLElement;
            const tagName = target.tagName.toLowerCase();
            const classList = target.classList;
            if (
                tagName === 'input' ||
                classList.contains('mantine-Switch-trackLabel') ||
                classList.contains('label-for-switch') ||
                classList.contains('mantine-Switch-thumb')
            ) {
                return;
            }
            push(`/automation/edit/${id}`);
        },
        [id, push],
    );

    const handleDeleteAutomation = useCallback(
        (e: BaseSyntheticEvent, id: string) => {
            e.stopPropagation();
            intendToDeleteAutomation(id);
        },
        [intendToDeleteAutomation],
    );
    return (
        <Card
            className={classes.cardItem}
            shadow="sm"
            padding="lg"
            radius="md"
            withBorder
            h={'140px'}
            onClick={handleMoveToDetail}
        >
            <Group justify="space-between" style={{ flexWrap: 'nowrap' }}>
                <Title order={6} lineClamp={1} className={classes.cardTitle}>
                    {name}
                </Title>
                <SwitchStyled
                    className={classes.switchStyle}
                    disabled={!isManager}
                    color="decaGreen.6"
                    labelPosition="left"
                    checked={currStatus === 'active'}
                    onChange={(e) => {
                        e.stopPropagation();
                        const _status = currStatus === 'active' ? 'inactive' : 'active';
                        setCurrStatus(_status);
                        onToggleActive(_status);
                    }}
                    label={
                        <Text
                            w={'auto'}
                            fz={14}
                            fw={700}
                            style={{ flexWrap: 'nowrap' }}
                            className="label-for-switch"
                        >
                            {t(currStatus === 'active' ? 'active' : 'inactive')}
                        </Text>
                    }
                />
            </Group>
            <Group mt="md" style={{ flexWrap: 'nowrap' }}>
                <Group justify="flex-end">
                    <IconHandClick size={20} className={classes.colorViolet} />
                    <Text fw={500} size={'14px'} color="navy.0" lineClamp={1}>
                        {descriptionAction}
                    </Text>
                </Group>
            </Group>
            <Group justify="space-between" mt="sm" mb="sm" style={{ flexWrap: 'nowrap' }}>
                <Text
                    size="sm"
                    color="dimmed"
                    style={{ flexWrap: 'nowrap' }}
                    lineClamp={1}
                    className={classes.cardDate}
                >
                    {formatDate(created)}
                </Text>
                <ActionIcon
                    disabled={!isManager}
                    variant="transparent"
                    color="navy.0"
                    className="action-icon-delete"
                    onClick={(e) => handleDeleteAutomation(e, id)}
                >
                    <IconTrash size={25} className="icon-delete" />
                </ActionIcon>
            </Group>
        </Card>
    );
}

function ConfirmDeleteAutomationModal() {
    const { classes } = useStyle();
    const {
        t,
        showConfirmDeleteModal,
        isManager,
        handleCloseConfirmDeleteModal,
        handleDeleteAutomation,
    } = useAutomationListContext();

    return (
        <Modal
            centered
            opened={showConfirmDeleteModal}
            withCloseButton={false}
            onClose={handleCloseConfirmDeleteModal}
        >
            <Modal.Header pt={0} pr={0}>
                <Modal.CloseButton iconSize={30} color="dark.7" className={classes.closeIcon} />
            </Modal.Header>
            <Modal.Body>
                <Center mb={'lg'}>
                    <Text>{t('delete_confirm_message')}</Text>
                </Center>
                <Center mb={'lg'}>
                    <Group>
                        <Button
                            size="compact-md"
                            color="red"
                            disabled={!isManager}
                            onClick={handleDeleteAutomation}
                        >
                            {t('delete_button')}
                        </Button>
                        <Button
                            size="compact-md"
                            color="dark"
                            variant="outline"
                            onClick={handleCloseConfirmDeleteModal}
                        >
                            {t('cancel_button')}
                        </Button>
                    </Group>
                </Center>
            </Modal.Body>
        </Modal>
    );
}
