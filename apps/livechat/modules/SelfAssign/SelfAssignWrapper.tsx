import { But<PERSON>, Container, Modal, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import PopupDetail from './PopupDetail';

import { COLOR_DEFAULT_BLACK } from '@resola-ai/shared-constants';
import { SELF_ASSIGN_ID_NAME } from '../../constants';
import { useUserContext } from '../userContext';
import { useThemeContext } from './themeContext';
import { useConversationActionContext } from '../conversationActionContext';
import { useTranslate } from '@tolgee/react';

const SelfAssignWrapper = ({}: { lang: string }) => {
    const { mainBgGray } = useThemeContext();
    const { t } = useTranslate('workspace');

    const { userProfile, userTeams } = useUserContext();
    const [opened, { open, close }] = useDisclosure(false);
    const { handleAssignEntityInCharge } = useConversationActionContext();

    const handleAssign = async () => {
        if (userTeams.length > 1) {
            open();
            return;
        }
        await handleAssignEntityInCharge(userProfile.id, userTeams[0].id);
    };

    return (
        <Container
            mx={0}
            p={20}
            bg={mainBgGray}
            id={SELF_ASSIGN_ID_NAME}
            sx={{
                position: 'absolute',
                bottom: 0,
                width: '100%',
            }}
        >
            <Text
                sx={{
                    fontWeight: 700,
                    fontSize: '14px',
                    lineHeight: '21.7px',
                    marginBottom: '10px',
                }}
                color={COLOR_DEFAULT_BLACK}
            >
                {t('main.title')}
            </Text>
            <Text
                sx={{
                    fontWeight: 400,
                    fontSize: '14px',
                    lineHeight: '21.7px',
                    marginBottom: '10px',
                }}
                color={'#495057'}
            >
                {t('main.description')}
            </Text>
            <Button
                sx={{
                    borderRadius: '8px',
                    fontWeight: 600,
                    fontSize: '16px',
                    lineHeight: '24.8px',
                }}
                color="navy.0"
                onClick={handleAssign}
            >
                {t('main.submit')}
            </Button>
            <Modal opened={opened} size={'500px'} onClose={close} centered withCloseButton={false}>
                <Modal.Header style={{ height: 36, minHeight: 'unset' }}>
                    <Modal.CloseButton className="" size={'lg'} />
                </Modal.Header>
                <Container pl={0} pr={0} mb={'lg'}>
                    <PopupDetail close={close} />
                </Container>
            </Modal>
        </Container>
    );
};

export default SelfAssignWrapper;
