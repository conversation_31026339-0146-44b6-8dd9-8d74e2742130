import React, { useCallback } from 'react';
import { But<PERSON>, Container, Flex, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { delay } from '../../../utils/common';
import { useConversationActionContext } from '../../conversationActionContext';
import { useUserContext } from '../../userContext';
import TeamSection from './TeamSection';
import { useTranslate } from '@tolgee/react';

interface Props {
    close: () => void;
}

const useStyle = createStyles((theme) => ({
    buttonConfirm: {
        minWidth: '84px',
        backgroundColor: theme.colors.navy[0],
        borderRadius: '8px',
        fontWeight: 600,
        fontSize: '14px',
        '&:hover': {
            backgroundColor: theme.colors.navy[1],
        },
    },
    buttonCancel: {
        minWidth: '86px',
        backgroundColor: 'white',
        borderWidth: '1.5px',
        borderRadius: '8px',
        fontWeight: 600,
        fontSize: '14px',
        color: '#5C5F66',
        borderColor: '#5C5F66',
        '&:hover': {
            backgroundColor: '#f0f0f0',
        },
    },
}));

const PopupDetail: React.FC<Props> = ({ close }) => {
    const { classes } = useStyle();
    const { userProfile, userTeams } = useUserContext();
    const [selectedTeam, setSelectedTeam] = React.useState<any>(null);
    const { t } = useTranslate('workspace');
    const {
        currentConversation,
        handleSelfAssignEntityInCharge,
        handleAssignEntityInCharge,
        isLoadingConversation,
    } = useConversationActionContext();

    const submitSelfAssign = useCallback(async () => {
        // assign this operator to this conversation
        console.log('Self Assign');
        await handleSelfAssignEntityInCharge();
        close();
    }, [close, handleSelfAssignEntityInCharge]);

    const submitTeamAssign = useCallback(
        async (teamId: string) => {
            // assign this operator to this conversation
            handleAssignEntityInCharge(userProfile.id, teamId);
            await delay(1000);
            close();
        },
        [close, handleAssignEntityInCharge, userProfile.id],
    );

    if (currentConversation && !currentConversation.teamId) {
        // show ui to assign this conversation to a team
        return (
            <Container p={0}>
                <Text
                    ta="center"
                    sx={{
                        fontWeight: 500,
                        fontSize: '16px',
                        lineHeight: '24.8px',
                    }}
                >
                    {t('popup.selfAssign.title')}
                </Text>
                <TeamSection onChange={setSelectedTeam} />
                <Flex
                    justify={'center'}
                    gap={20}
                    m={30}
                    sx={{
                        display: userTeams.length > 0 ? 'flex' : 'none',
                    }}
                >
                    <Button
                        disabled={isLoadingConversation}
                        className={classes.buttonCancel}
                        onClick={close}
                    >
                        {t('popup.selfAssign.cancel')}
                    </Button>
                    <Button
                        disabled={isLoadingConversation}
                        loading={isLoadingConversation}
                        className={classes.buttonConfirm}
                        onClick={async () => {
                            // console.log('selectedTeam', selectedTeam);
                            if (selectedTeam && selectedTeam.id) {
                                await submitTeamAssign(selectedTeam.id);
                            }
                        }}
                    >
                        {t('popup.selfAssign.submit')}
                    </Button>
                </Flex>
                <Text
                    ta={'center'}
                    color={'#495057'}
                    sx={{
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '21.7px',
                        whiteSpace: 'pre',
                    }}
                >
                    {t('popup.selfAssign.description')}
                </Text>
            </Container>
        );
    }

    return (
        <Container p={0}>
            <Text
                ta="center"
                sx={{
                    fontWeight: 500,
                    fontSize: '16px',
                    lineHeight: '24.8px',
                }}
            >
                {t('popup.selfAssign.title')}
            </Text>
            <Flex
                justify={'center'}
                gap={20}
                m={30}
                sx={{
                    display: userTeams.length > 0 ? 'flex' : 'none',
                }}
            >
                <Button
                    disabled={isLoadingConversation}
                    className={classes.buttonCancel}
                    onClick={close}
                >
                    {t('popup.selfAssign.cancel')}
                </Button>
                <Button
                    disabled={isLoadingConversation}
                    loading={isLoadingConversation}
                    className={classes.buttonConfirm}
                    onClick={submitSelfAssign}
                >
                    {t('popup.selfAssign.submit')}
                </Button>
            </Flex>
            <Text
                ta={'center'}
                c={'#495057'}
                sx={{
                    fontWeight: 400,
                    fontSize: '14px',
                    lineHeight: '21.7px',
                    whiteSpace: 'pre',
                }}
            >
                {t('popup.selfAssign.description')}
            </Text>
        </Container>
    );
};

export default PopupDetail;
